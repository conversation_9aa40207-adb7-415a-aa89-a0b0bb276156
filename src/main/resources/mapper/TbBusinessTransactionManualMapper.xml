<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessTransactionManualMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessTransactionManual">
        <id column="id" property="id" />
        <result column="transaction_id" property="transactionId" />
        <result column="user_id" property="userId" />
        <result column="type" property="type" />
        <result column="ratio" property="ratio" />
        <result column="amount" property="amount" />
        <result column="percentage" property="percentage" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, transaction_id, user_id, type, ratio, amount, percentage, 
        revision, create_user, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
