package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import mixc.be.rsms.business.domain.pojo.TbDataConfCommissionTypeItemSnap;
import mixc.be.rsms.business.mapper.TbDataConfCommissionTypeItemSnapMapper;
import mixc.be.rsms.business.service.IDataConfCommissionTypeItemSnapService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 财务管理-提成配置配置类型子表(快照) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class DataConfCommissionTypeItemSnapServiceImpl extends ServiceImpl<TbDataConfCommissionTypeItemSnapMapper, TbDataConfCommissionTypeItemSnap> implements IDataConfCommissionTypeItemSnapService {

}
