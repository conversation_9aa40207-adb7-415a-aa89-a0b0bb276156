package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionAuto;
import mixc.be.rsms.business.service.IBusinessTransactionAutoService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 成交报告-业绩分配信息-自动分配 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@RestController
@RequestMapping("/business-transaction-auto")
@Tag(name = "成交报告-业绩分配信息-自动分配")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class BusinessTransactionAutoController {

    private final IBusinessTransactionAutoService businessTransactionAutoService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TbBusinessTransactionAuto>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long transactionId) {
        Page<TbBusinessTransactionAuto> page = new Page<>(current, size);
        LambdaQueryWrapper<TbBusinessTransactionAuto> wrapper = new LambdaQueryWrapper<>();
        if (transactionId != null) {
            wrapper.eq(TbBusinessTransactionAuto::getTransactionId, transactionId);
        }
        IPage<TbBusinessTransactionAuto> result = businessTransactionAutoService.page(page, wrapper);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "根据ID查询")
    @GetMapping("/{id}")
    public ResultVo<TbBusinessTransactionAuto> getById(@PathVariable Long id) {
        TbBusinessTransactionAuto result = businessTransactionAutoService.getById(id);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "根据成交报告ID查询列表")
    @GetMapping("/list/{transactionId}")
    public ResultVo<List<TbBusinessTransactionAuto>> listByTransactionId(@PathVariable Long transactionId) {
        LambdaQueryWrapper<TbBusinessTransactionAuto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbBusinessTransactionAuto::getTransactionId, transactionId);
        List<TbBusinessTransactionAuto> result = businessTransactionAutoService.list(wrapper);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "新增")
    @PostMapping
    public ResultVo<Boolean> save(@RequestBody TbBusinessTransactionAuto entity) {
        boolean result = businessTransactionAutoService.save(entity);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "修改")
    @PutMapping
    public ResultVo<Boolean> updateById(@RequestBody TbBusinessTransactionAuto entity) {
        boolean result = businessTransactionAutoService.updateById(entity);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ResultVo<Boolean> removeById(@PathVariable Long id) {
        boolean result = businessTransactionAutoService.removeById(id);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public ResultVo<Boolean> removeByIds(@RequestBody List<Long> ids) {
        boolean result = businessTransactionAutoService.removeByIds(ids);
        return ResultVoUtil.success(result);
    }
}
