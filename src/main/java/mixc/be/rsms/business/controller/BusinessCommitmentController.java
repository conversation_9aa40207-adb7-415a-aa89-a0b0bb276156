package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.pojo.TbBusinessCommitment;
import mixc.be.rsms.business.service.IBusinessCommitmentService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 提成主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@RestController
@RequestMapping("/business-commitment")
@Tag(name = "提成主表")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class BusinessCommitmentController {

    private final IBusinessCommitmentService businessCommitmentService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TbBusinessCommitment>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String commitmentCode,
            @RequestParam(required = false) String commitmentType,
            @RequestParam(required = false) Integer distributionStatus) {
        Page<TbBusinessCommitment> page = new Page<>(current, size);
        LambdaQueryWrapper<TbBusinessCommitment> wrapper = new LambdaQueryWrapper<>();
        if (commitmentCode != null) {
            wrapper.like(TbBusinessCommitment::getCommitmentCode, commitmentCode);
        }
        if (commitmentType != null) {
            wrapper.eq(TbBusinessCommitment::getCommitmentType, commitmentType);
        }
        if (distributionStatus != null) {
            wrapper.eq(TbBusinessCommitment::getDistributionStatus, distributionStatus);
        }
        wrapper.orderByDesc(TbBusinessCommitment::getCreateTime);
        IPage<TbBusinessCommitment> result = businessCommitmentService.page(page, wrapper);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "根据ID查询")
    @GetMapping("/{id}")
    public ResultVo<TbBusinessCommitment> getById(@PathVariable Long id) {
        TbBusinessCommitment result = businessCommitmentService.getById(id);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "根据提成编码查询")
    @GetMapping("/code/{commitmentCode}")
    public ResultVo<TbBusinessCommitment> getByCode(@PathVariable String commitmentCode) {
        LambdaQueryWrapper<TbBusinessCommitment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbBusinessCommitment::getCommitmentCode, commitmentCode);
        TbBusinessCommitment result = businessCommitmentService.getOne(wrapper);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "查询未发放的提成列表")
    @GetMapping("/undistributed")
    public ResultVo<List<TbBusinessCommitment>> listUndistributed() {
        LambdaQueryWrapper<TbBusinessCommitment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbBusinessCommitment::getDistributionStatus, 0);
        wrapper.orderByDesc(TbBusinessCommitment::getCreateTime);
        List<TbBusinessCommitment> result = businessCommitmentService.list(wrapper);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "新增")
    @PostMapping
    public ResultVo<Boolean> save(@RequestBody TbBusinessCommitment entity) {
        boolean result = businessCommitmentService.save(entity);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "修改")
    @PutMapping
    public ResultVo<Boolean> updateById(@RequestBody TbBusinessCommitment entity) {
        boolean result = businessCommitmentService.updateById(entity);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "标记为已发放")
    @PutMapping("/distribute/{id}")
    public ResultVo<Boolean> markAsDistributed(@PathVariable Long id) {
        TbBusinessCommitment entity = new TbBusinessCommitment();
        entity.setId(id);
        entity.setDistributionStatus(1);
        boolean result = businessCommitmentService.updateById(entity);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ResultVo<Boolean> removeById(@PathVariable Long id) {
        boolean result = businessCommitmentService.removeById(id);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public ResultVo<Boolean> removeByIds(@RequestBody List<Long> ids) {
        boolean result = businessCommitmentService.removeByIds(ids);
        return ResultVoUtil.success(result);
    }
}
