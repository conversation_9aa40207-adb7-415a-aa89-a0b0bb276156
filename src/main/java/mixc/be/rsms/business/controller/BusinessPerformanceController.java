package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.pojo.TbBusinessPerformance;
import mixc.be.rsms.business.service.IBusinessPerformanceService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 业绩主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@RestController
@RequestMapping("/business-performance")
@Tag(name = "业绩主表")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class BusinessPerformanceController {

    private final IBusinessPerformanceService businessPerformanceService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TbBusinessPerformance>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long transactionId,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String performanceType) {
        Page<TbBusinessPerformance> page = new Page<>(current, size);
        LambdaQueryWrapper<TbBusinessPerformance> wrapper = new LambdaQueryWrapper<>();
        if (transactionId != null) {
            wrapper.eq(TbBusinessPerformance::getTransactionId, transactionId);
        }
        if (userId != null) {
            wrapper.eq(TbBusinessPerformance::getUserId, userId);
        }
        if (performanceType != null) {
            wrapper.eq(TbBusinessPerformance::getPerformanceType, performanceType);
        }
        wrapper.orderByDesc(TbBusinessPerformance::getCreateTime);
        IPage<TbBusinessPerformance> result = businessPerformanceService.page(page, wrapper);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "根据ID查询")
    @GetMapping("/{id}")
    public ResultVo<TbBusinessPerformance> getById(@PathVariable Long id) {
        TbBusinessPerformance result = businessPerformanceService.getById(id);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "根据成交报告ID查询列表")
    @GetMapping("/list/{transactionId}")
    public ResultVo<List<TbBusinessPerformance>> listByTransactionId(@PathVariable Long transactionId) {
        LambdaQueryWrapper<TbBusinessPerformance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbBusinessPerformance::getTransactionId, transactionId);
        List<TbBusinessPerformance> result = businessPerformanceService.list(wrapper);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "根据用户ID查询业绩列表")
    @GetMapping("/user/{userId}")
    public ResultVo<List<TbBusinessPerformance>> listByUserId(@PathVariable Long userId) {
        LambdaQueryWrapper<TbBusinessPerformance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbBusinessPerformance::getUserId, userId);
        wrapper.orderByDesc(TbBusinessPerformance::getPerformanceTime);
        List<TbBusinessPerformance> result = businessPerformanceService.list(wrapper);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "新增")
    @PostMapping
    public ResultVo<Boolean> save(@RequestBody TbBusinessPerformance entity) {
        boolean result = businessPerformanceService.save(entity);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "修改")
    @PutMapping
    public ResultVo<Boolean> updateById(@RequestBody TbBusinessPerformance entity) {
        boolean result = businessPerformanceService.updateById(entity);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ResultVo<Boolean> removeById(@PathVariable Long id) {
        boolean result = businessPerformanceService.removeById(id);
        return ResultVoUtil.success(result);
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public ResultVo<Boolean> removeByIds(@RequestBody List<Long> ids) {
        boolean result = businessPerformanceService.removeByIds(ids);
        return ResultVoUtil.success(result);
    }
}
