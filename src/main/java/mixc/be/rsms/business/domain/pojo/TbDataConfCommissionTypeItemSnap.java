package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 财务管理-提成配置配置类型子表(快照)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@TableName("tb_data_conf_commission_type_item_snap")
@EqualsAndHashCode(callSuper = true)
public class TbDataConfCommissionTypeItemSnap extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 配置类型表id
     */
    @TableField("type_id")
    private Long typeId;

    /**
     * 业绩提成金额起始
     */
    @TableField("amount_start")
    private BigDecimal amountStart;

    /**
     * 业绩提成金额结束（包含）
     */
    @TableField("amount_end")
    private BigDecimal amountEnd;

    /**
     * 提成比例
     */
    @TableField("commission_ratio")
    private BigDecimal commissionRatio;
}
