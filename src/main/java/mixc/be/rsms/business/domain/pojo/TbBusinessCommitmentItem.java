package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 提成主表-明细行
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@TableName("tb_business_commitment_item")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessCommitmentItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 提成主表id
     */
    @TableField("commitment_id")
    private Long commitmentId;

    /**
     * 成交报告id
     */
    @TableField("transaction_id")
    private Long transactionId;

    /**
     * 业绩类型
     */
    @TableField("performance_type")
    private String performanceType;

    /**
     * 业务类型
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 提成类型
     */
    @TableField("commitment_type")
    private String commitmentType;

    /**
     * 提成金额
     */
    @TableField("commitment_amount")
    private BigDecimal commitmentAmount;
}
