package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 业绩主表-明细行
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@TableName("tb_business_performance_item")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessPerformanceItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 业绩表id
     */
    @TableField("performance_id")
    private Long performanceId;

    /**
     * 税率
     */
    @TableField("tax_rate")
    private BigDecimal taxRate;

    /**
     * 应收不含税金额
     */
    @TableField("receivable_excluding_tax_amount")
    private BigDecimal receivableExcludingTaxAmount;

    /**
     * 实收不含税金额
     */
    @TableField("real_time_excluding_tax_amount")
    private BigDecimal realTimeExcludingTaxAmount;
}
