package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 财务管理-提成配置配置类型(快照)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Data
@TableName("tb_data_conf_commission_type_snap")
@EqualsAndHashCode(callSuper = true)
public class TbDataConfCommissionTypeSnap extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 父表id
     */
    @TableField("commitment_item_id")
    private Long commitmentItemId;

    /**
     * 配置类型(second_hand_house:二手房租赁&买卖业绩提成,new_house:新房业绩提成,parking_storage:车储租赁&买卖,management:管理业绩)
     */
    @TableField("config_type")
    private String configType;

    /**
     * 提成类型(excess_progressive:超额累进,full_commission:通提,fixed_ratio:固定比例)
     */
    @TableField("commission_type")
    private String commissionType;

    /**
     * 是否合并到二手房租赁&业绩提成(0 否 1是 )
     */
    @TableField("merge_flag")
    private Integer mergeFlag;
}
